import {
  View,
  Text,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { useState, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { colors } from '@/constants';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FlashList } from '@shopify/flash-list';
import { useAuthStore } from '@/stores';
import PageHeader from '@/components/PageHeader';
import handleAPICall from '@/utils/HandleApiCall';
import CustomButton from '@/components/CustomButton';
import HorizontalSeparator from '@/components/HorizontalSeparator';
import CustomEmptyMessage from '@/components/CustomEmptyMessage';
import CustomErrorMessage from '@/components/CustomErrorMessage';

// Expo Vector Icons
import { MaterialIcons, Ionicons, Feather, MaterialCommunityIcons } from '@expo/vector-icons';

import moment from 'moment';
import * as Clipboard from 'expo-clipboard';
import * as Haptics from 'expo-haptics';
import Toast from 'react-native-toast-message';

const wifi = () => {
  const { user } = useAuthStore();

  if (!user) {
    return (
      <SafeAreaView className="h-full items-center justify-center bg-white">
        <ActivityIndicator size="large" color={colors.orange} />
      </SafeAreaView>
    );
  }

  if (!user.cardno) {
    return (
      <SafeAreaView className="h-full items-center justify-center bg-white">
        <Text className="font-pregular text-lg text-red-500">Missing card number</Text>
      </SafeAreaView>
    );
  }

  // State management
  const [refreshing, setRefreshing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPermanentSubmitting, setIsPermanentSubmitting] = useState(false);

  // Fetch temporary WiFi passwords
  const fetchWifiPasswords = async () => {
    return new Promise((resolve, reject) => {
      handleAPICall(
        'GET',
        '/wifi',
        {
          cardno: user.cardno,
        },
        null,
        (res: any) => {
          resolve(Array.isArray(res.data) ? res.data : []);
        },
        (error: any) => {
          reject(new Error(error?.message || 'Failed to fetch wifi passwords'));
        }
      );
    });
  };

  // Fetch permanent WiFi code status
  const fetchPermanentWifiCode = async () => {
    return new Promise((resolve, reject) => {
      handleAPICall(
        'GET',
        '/wifi/permanent',
        {
          cardno: user.cardno,
        },
        null,
        (res: any) => {
          resolve(res.data || null);
        },
        (error: any) => {
          reject(new Error(error?.message || 'Failed to fetch permanent wifi code'));
        }
      );
    });
  };

  const {
    isLoading,
    isError,
    error,
    data: wifiList,
    refetch,
  }: any = useQuery({
    queryKey: ['wifi', user.cardno],
    queryFn: fetchWifiPasswords,
    staleTime: 1000 * 60 * 30,
    enabled: !!user.cardno,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  const {
    isLoading: isPermanentLoading,
    isError: isPermanentError,
    data: permanentWifiData,
    refetch: refetchPermanent,
  }: any = useQuery({
    queryKey: ['wifi-permanent', user.cardno],
    queryFn: fetchPermanentWifiCode,
    staleTime: 1000 * 60 * 30,
    enabled: !!user.cardno,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Generate temporary WiFi code
  const generateNewWifiCode = async () => {
    return new Promise((resolve, reject) => {
      handleAPICall(
        'GET',
        '/wifi/generate',
        { cardno: user.cardno },
        null,
        (res: any) => {
          resolve(res.data);
        },
        (error: any) => {
          reject(new Error(error?.message || 'Failed to generate new wifi code'));
        }
      );
    });
  };

  // Request permanent WiFi code
  const requestPermanentWifiCode = async () => {
    return new Promise((resolve, reject) => {
      handleAPICall(
        'POST',
        '/wifi/permanent',
        null,
        { cardno: user.cardno },
        (res: any) => {
          resolve(res.data);
        },
        (error: any) => {
          reject(new Error(error?.message || 'Failed to request permanent wifi code'));
        }
      );
    });
  };

  const handleGenerateCode = async () => {
    setIsSubmitting(true);
    try {
      await generateNewWifiCode();
      await refetch();
      Toast.show({
        type: 'success',
        text1: 'WiFi code generated',
        text2: 'Your new temporary code is ready',
        swipeable: false,
      });
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Generation failed',
        text2: error.message || 'Please try again',
        swipeable: false,
      });
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRequestPermanentCode = async () => {
    setIsPermanentSubmitting(true);
    try {
      await requestPermanentWifiCode();
      Toast.show({
        type: 'success',
        text1: 'Permanent WiFi code requested',
        text2: 'Your request is being reviewed',
        swipeable: false,
      });
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      await refetchPermanent();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Request failed',
        text2: error.message || 'Please try again',
        swipeable: false,
      });
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setIsPermanentSubmitting(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    Promise.all([refetch(), refetchPermanent()]).finally(() => setRefreshing(false));
  }, [refetch, refetchPermanent]);

  // Copy to clipboard helper
  const copyToClipboard = async (text: string, successMessage?: string) => {
    await Clipboard.setStringAsync(text);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    Toast.show({
      type: 'info',
      text1: successMessage || 'Copied to clipboard',
      swipeable: false,
    });
  };

  // Render temporary WiFi code item
  const renderTemporaryItem = ({ item }: any) => {
    const isExpiringSoon = moment().add(3, 'days').isAfter(moment(item.createdAt).add(15, 'days'));
    const daysLeft = moment(item.createdAt).add(15, 'days').diff(moment(), 'days');

    return (
      <View className="mx-4 mb-4 rounded-2xl border border-gray-100/50 bg-white shadow-lg">
        {/* Code Display */}
        <View className="p-6">
          <View className="mb-5 rounded-xl border border-blue-100/60 bg-gradient-to-br from-blue-50 to-indigo-50 p-5">
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <View className="mb-2 flex-row items-center">
                  <Ionicons name="time-outline" size={14} color="#3B82F6" />
                  <Text className="ml-1.5 font-pmedium text-xs uppercase tracking-wide text-blue-600">
                    Temporary Code
                  </Text>
                </View>
                <Text className="font-mono font-psemibold text-2xl tracking-wider text-gray-900">
                  {item.password}
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => copyToClipboard(item.password, 'WiFi code copied')}
                className="ml-4 h-12 w-12 items-center justify-center rounded-xl border border-blue-200/60 bg-white/80 shadow-sm"
                style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                <Feather name="copy" size={18} color="#6B7280" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Status and Details */}
          <View className="gap-y-4">
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <MaterialIcons
                  name="info-outline"
                  size={16}
                  color="#6B7280"
                  style={{ marginRight: 6 }}
                />
                <Text className="font-pmedium text-sm text-gray-700">Status</Text>
              </View>
              <View
                className={`rounded-full px-3 py-1.5 ${
                  isExpiringSoon
                    ? 'border border-amber-200 bg-amber-100'
                    : 'border border-emerald-200 bg-emerald-100'
                }`}>
                <Text
                  className={`font-pmedium text-xs ${
                    isExpiringSoon ? 'text-amber-700' : 'text-emerald-700'
                  }`}>
                  {isExpiringSoon ? `${daysLeft} days left` : 'Active'}
                </Text>
              </View>
            </View>

            <View className="h-px bg-gray-100" />

            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <MaterialIcons
                  name="schedule"
                  size={16}
                  color="#6B7280"
                  style={{ marginRight: 6 }}
                />
                <Text className="font-pmedium text-sm text-gray-700">Generated</Text>
              </View>
              <Text className="font-pregular text-sm text-gray-600">
                {moment(item.createdAt).format('MMM D, YYYY')}
              </Text>
            </View>

            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <MaterialIcons name="event" size={16} color="#6B7280" style={{ marginRight: 6 }} />
                <Text className="font-pmedium text-sm text-gray-700">Expires</Text>
              </View>
              <Text className="font-pregular text-sm text-gray-600">
                {moment(item.createdAt).add(15, 'days').format('MMM D, YYYY')}
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  // Empty state for permanent WiFi
  const renderPermanentEmptyState = () => (
    <View className="mx-4 mb-6 rounded-2xl border border-gray-100/50 bg-white shadow-lg">
      <View className="items-center p-8">
        <View className="mb-6 h-20 w-20 items-center justify-center rounded-full bg-purple-100">
          <MaterialIcons name="security" size={32} color="#9333EA" />
        </View>
        <Text className="mb-3 text-center font-psemibold text-xl text-gray-900">
          No Permanent Access
        </Text>
        <Text className="mb-8 max-w-sm text-center font-pregular text-sm leading-relaxed text-gray-600">
          Get unlimited WiFi access without expiration dates. Perfect for long-term users and staff
          members.
        </Text>
        <CustomButton
          text={'Request Permanent Access'}
          handlePress={handleRequestPermanentCode}
          containerStyles={'px-8 py-4 min-h-[52px] shadow-lg'}
          textStyles={'text-sm font-pmedium text-white'}
          isLoading={isPermanentSubmitting}
        />
      </View>
    </View>
  );

  // Render permanent WiFi code section
  const renderPermanentWifiSection = () => {
    const getStatusConfig = (status: string) => {
      switch (status) {
        case 'approved':
          return {
            bg: 'bg-emerald-50',
            text: 'text-emerald-700',
            border: 'border-emerald-200',
            label: 'Approved',
            icon: 'check-circle',
          };
        case 'rejected':
          return {
            bg: 'bg-red-50',
            text: 'text-red-700',
            border: 'border-red-200',
            label: 'Rejected',
            icon: 'cancel',
          };
        case 'pending':
        default:
          return {
            bg: 'bg-amber-50',
            text: 'text-amber-700',
            border: 'border-amber-200',
            label: 'Under Review',
            icon: 'pending',
          };
      }
    };

    return (
      <View className="mt-2">
        {/* Section Header */}
        <View className="mb-6 px-4">
          <View className="mb-3 flex-row items-center">
            <View className="mr-4 h-10 w-10 items-center justify-center rounded-xl bg-purple-100">
              <MaterialIcons name="security" size={20} color="#9333EA" />
            </View>
            <View className="flex-1">
              <Text className="font-psemibold text-xl text-gray-900">Permanent Access</Text>
              <Text className="mt-0.5 font-pregular text-sm text-gray-600">
                Long-term WiFi access without expiration
              </Text>
            </View>
          </View>
        </View>

        {isPermanentLoading ? (
          <View className="mx-4 mb-6 items-center justify-center rounded-2xl border border-gray-100/50 bg-white p-10 shadow-lg">
            <ActivityIndicator size="large" color={colors.orange} />
            <Text className="mt-4 font-pregular text-sm text-gray-600">
              Loading permanent access status...
            </Text>
          </View>
        ) : isPermanentError ? (
          <View className="mx-4 mb-6 rounded-2xl border border-gray-100/50 bg-white p-6 shadow-lg">
            <CustomErrorMessage />
          </View>
        ) : !permanentWifiData ? (
          renderPermanentEmptyState()
        ) : (
          <View className="mx-4 mb-6 rounded-2xl border border-gray-100/50 bg-white shadow-lg">
            <View className="p-6">
              {/* Status Badge */}
              <View className="mb-5 flex-row items-center justify-between">
                <View
                  className={`flex-row items-center rounded-full px-4 py-2 ${
                    getStatusConfig(permanentWifiData.status).bg
                  } ${getStatusConfig(permanentWifiData.status).border} border`}>
                  <MaterialIcons
                    name={getStatusConfig(permanentWifiData.status).icon as any}
                    size={16}
                    color={getStatusConfig(permanentWifiData.status).text.replace('text-', '#')}
                    style={{ marginRight: 6 }}
                  />
                  <Text
                    className={`font-pmedium text-sm ${
                      getStatusConfig(permanentWifiData.status).text
                    }`}>
                    {getStatusConfig(permanentWifiData.status).label}
                  </Text>
                </View>
              </View>

              {permanentWifiData.status === 'approved' && permanentWifiData.password && (
                <>
                  <View className="mb-5 rounded-xl border border-purple-100/60 bg-gradient-to-br from-purple-50 to-indigo-50 p-5">
                    <View className="flex-row items-center justify-between">
                      <View className="flex-1">
                        <View className="mb-2 flex-row items-center">
                          <MaterialIcons name="security" size={14} color="#9333EA" />
                          <Text className="ml-1.5 font-pmedium text-xs uppercase tracking-wide text-purple-600">
                            Permanent Code
                          </Text>
                        </View>
                        <Text className="font-mono font-psemibold text-2xl tracking-wider text-gray-900">
                          {permanentWifiData.password}
                        </Text>
                      </View>
                      <TouchableOpacity
                        onPress={() =>
                          copyToClipboard(permanentWifiData.password, 'Permanent code copied')
                        }
                        className="ml-4 h-12 w-12 items-center justify-center rounded-xl border border-purple-200/60 bg-white/80 shadow-sm"
                        style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                        <Feather name="copy" size={18} color="#6B7280" />
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View className="gap-y-4">
                    <View className="flex-row items-center justify-between">
                      <View className="flex-row items-center">
                        <MaterialIcons
                          name="check-circle"
                          size={16}
                          color="#6B7280"
                          style={{ marginRight: 6 }}
                        />
                        <Text className="font-pmedium text-sm text-gray-700">Approved</Text>
                      </View>
                      <Text className="font-pregular text-sm text-gray-600">
                        {moment(permanentWifiData.updatedAt).format('MMM D, YYYY')}
                      </Text>
                    </View>

                    <View className="h-px bg-gray-100" />

                    <View className="flex-row items-center justify-between">
                      <View className="flex-row items-center">
                        <MaterialIcons
                          name="all-inclusive"
                          size={16}
                          color="#6B7280"
                          style={{ marginRight: 6 }}
                        />
                        <Text className="font-pmedium text-sm text-gray-700">Validity</Text>
                      </View>
                      <View className="rounded-full border border-emerald-200 bg-emerald-100 px-3 py-1.5">
                        <Text className="font-pmedium text-xs text-emerald-700">No Expiry</Text>
                      </View>
                    </View>
                  </View>
                </>
              )}

              {permanentWifiData.status === 'pending' && (
                <View className="gap-y-5">
                  <View className="rounded-xl border border-amber-100 bg-amber-50 p-4">
                    <View className="flex-row items-start">
                      <MaterialIcons
                        name="pending"
                        size={20}
                        color="#D97706"
                        style={{ marginRight: 8, marginTop: 2 }}
                      />
                      <View className="flex-1">
                        <Text className="mb-1 font-pmedium text-amber-900">
                          Request Under Review
                        </Text>
                        <Text className="font-pregular text-sm leading-relaxed text-amber-800">
                          Your permanent WiFi access request is currently being reviewed by our
                          team. You'll receive a notification once a decision has been made.
                        </Text>
                      </View>
                    </View>
                  </View>

                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center">
                      <MaterialIcons
                        name="schedule"
                        size={16}
                        color="#6B7280"
                        style={{ marginRight: 6 }}
                      />
                      <Text className="font-pmedium text-sm text-gray-700">Requested</Text>
                    </View>
                    <Text className="font-pregular text-sm text-gray-600">
                      {moment(permanentWifiData.createdAt).format('MMM D, YYYY')}
                    </Text>
                  </View>
                </View>
              )}

              {permanentWifiData.status === 'rejected' && (
                <View className="gap-y-5">
                  <View className="rounded-xl border border-red-100 bg-red-50 p-4">
                    <View className="flex-row items-start">
                      <MaterialIcons
                        name="cancel"
                        size={20}
                        color="#DC2626"
                        style={{ marginRight: 8, marginTop: 2 }}
                      />
                      <View className="flex-1">
                        <Text className="mb-1 font-pmedium text-red-900">Request Not Approved</Text>
                        <Text className="font-pregular text-sm leading-relaxed text-red-800">
                          Your permanent WiFi access request was not approved at this time. You can
                          submit a new request below.
                        </Text>
                      </View>
                    </View>
                  </View>

                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center">
                      <MaterialIcons
                        name="event"
                        size={16}
                        color="#6B7280"
                        style={{ marginRight: 6 }}
                      />
                      <Text className="font-pmedium text-sm text-gray-700">Reviewed</Text>
                    </View>
                    <Text className="font-pregular text-sm text-gray-600">
                      {moment(permanentWifiData.updatedAt).format('MMM D, YYYY')}
                    </Text>
                  </View>

                  {permanentWifiData.reason && (
                    <View className="rounded-xl border border-gray-200 bg-gray-50 p-4">
                      <Text className="mb-2 font-pmedium text-sm text-gray-700">
                        Reason for rejection:
                      </Text>
                      <Text className="font-pregular text-sm leading-relaxed text-gray-600">
                        {permanentWifiData.reason}
                      </Text>
                    </View>
                  )}

                  <CustomButton
                    text={'Submit New Request'}
                    handlePress={handleRequestPermanentCode}
                    containerStyles={'px-6 py-4 mt-4 min-h-[52px] shadow-lg'}
                    textStyles={'text-sm font-pmedium text-white'}
                    isLoading={isPermanentSubmitting}
                  />
                </View>
              )}
            </View>
          </View>
        )}
      </View>
    );
  };

  // Empty state for temporary codes
  const renderTemporaryEmptyState = () => (
    <View className="mx-4 mb-4 rounded-2xl border border-gray-100/50 bg-white shadow-lg">
      <View className="items-center p-8">
        <View className="mb-6 h-20 w-20 items-center justify-center rounded-full bg-blue-100">
          <MaterialCommunityIcons name="wifi-plus" size={32} color="#3B82F6" />
        </View>
        <Text className="mb-3 text-center font-psemibold text-xl text-gray-900">
          No Temporary Codes
        </Text>
        <Text className="mb-8 max-w-sm text-center font-pregular text-sm leading-relaxed text-gray-600">
          Generate temporary WiFi codes for guests or short-term access. Each code is valid for 15
          days.
        </Text>
        <CustomButton
          text={'Generate First Code'}
          handlePress={handleGenerateCode}
          containerStyles={'px-8 py-4 min-h-[52px] shadow-lg'}
          textStyles={'text-sm font-pmedium text-white'}
          isLoading={isSubmitting}
        />
      </View>
    </View>
  );

  const renderTemporarySection = () => (
    <View>
      {/* Section Header */}
      <View className="mb-6 px-4">
        <View className="mb-3 flex-row items-center">
          <View className="mr-4 h-10 w-10 items-center justify-center rounded-xl bg-blue-100">
            <Ionicons name="time-outline" size={20} color="#3B82F6" />
          </View>
          <View className="flex-1">
            <Text className="font-psemibold text-xl text-gray-900">Temporary Codes</Text>
            <Text className="mt-0.5 font-pregular text-sm text-gray-600">
              {wifiList?.length > 0
                ? `${wifiList.length} of 3 codes • Valid for 15 days each`
                : 'Generate codes for short-term access'}
            </Text>
          </View>
        </View>
      </View>

      {(!wifiList || wifiList.length === 0) && !isLoading && renderTemporaryEmptyState()}
    </View>
  );

  const renderTemporaryFooter = () => (
    <View className="mx-4 mt-4 pb-8">
      {isLoading && (
        <View className="items-center justify-center py-10">
          <ActivityIndicator size="large" color={colors.orange} />
          <Text className="mt-4 font-pregular text-sm text-gray-600">
            Loading temporary codes...
          </Text>
        </View>
      )}
      {wifiList && wifiList.length > 0 && !isLoading && (
        <View className="rounded-2xl border border-gray-100/50 bg-white p-6 shadow-lg">
          <CustomButton
            text={
              wifiList.length >= 3
                ? 'Maximum Limit Reached'
                : `Generate New Code (${wifiList.length}/3)`
            }
            handlePress={handleGenerateCode}
            containerStyles={'px-6 py-4 min-h-[52px] shadow-lg'}
            textStyles={'text-sm font-pmedium text-white'}
            isLoading={isSubmitting}
            isDisabled={wifiList.length >= 3}
          />
          {wifiList.length >= 3 && (
            <View className="mt-4 rounded-xl border border-amber-100 bg-amber-50 p-4">
              <View className="flex-row items-start">
                <MaterialIcons
                  name="info"
                  size={20}
                  color="#D97706"
                  style={{ marginRight: 8, marginTop: 1 }}
                />
                <Text className="flex-1 font-pregular text-sm leading-relaxed text-amber-800">
                  You've reached the maximum limit of 3 temporary codes. Wait for existing codes to
                  expire or contact support for assistance.
                </Text>
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  );

  // Calculate total count for header
  const getTotalCount = () => {
    const tempCount = wifiList?.length || 0;
    const permCount = permanentWifiData && permanentWifiData.status === 'approved' ? 1 : 0;
    return tempCount + permCount;
  };

  // Create unified data for FlashList (Permanent first, then temporary)
  const createUnifiedData = () => {
    const data = [];

    // Add permanent section FIRST
    data.push({ type: 'permanent-section' });

    // Add temporary section header
    data.push({ type: 'temporary-header' });

    // Add temporary codes
    if (wifiList && wifiList.length > 0) {
      wifiList.forEach((item: any) => {
        data.push({ type: 'temporary-item', data: item });
      });
    }

    // Add temporary footer
    data.push({ type: 'temporary-footer' });

    return data;
  };

  const renderUnifiedItem = ({ item }: any) => {
    switch (item.type) {
      case 'permanent-section':
        return renderPermanentWifiSection();
      case 'temporary-header':
        return renderTemporarySection();
      case 'temporary-item':
        return renderTemporaryItem({ item: item.data });
      case 'temporary-footer':
        return renderTemporaryFooter();
      default:
        return null;
    }
  };

  if (isError && isPermanentError)
    return (
      <SafeAreaView className="h-full bg-white">
        <PageHeader title={'WiFi Access'} />
        <View className="flex-1 items-center justify-center px-4">
          <CustomErrorMessage />
        </View>
      </SafeAreaView>
    );

  return (
    <SafeAreaView className="h-full bg-gray-50">
      <PageHeader title={`WiFi Access${getTotalCount() > 0 ? ` (${getTotalCount()})` : ''}`} />

      <FlashList
        className="flex-1"
        contentContainerStyle={{ paddingTop: 20, paddingBottom: 24 }}
        data={createUnifiedData()}
        showsVerticalScrollIndicator={false}
        estimatedItemSize={250}
        renderItem={renderUnifiedItem}
        keyExtractor={(item, index) => `${item.type}-${index}`}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.orange]}
            tintColor={colors.orange}
          />
        }
      />
    </SafeAreaView>
  );
};

export default wifi;
